'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useGerenciamentoAgendamentos } from '@/hooks/useGerenciamentoAgendamentos';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ListaAgendamentos } from '@/components/agendamentos/ListaAgendamentos';
import { FiltrosAgendamentos } from '@/components/agendamentos/FiltrosAgendamentos';
import { ModalDetalhesAgendamento } from '@/components/agendamentos/ModalDetalhesAgendamento';
import { AgendamentoCompleto } from '@/types/agendamentos';

function GerenciamentoAgendamentosColaborador() {
  const { user } = useAuth();
  const [agendamentoSelecionado, setAgendamentoSelecionado] = useState<AgendamentoCompleto | null>(null);
  const [mostrarModal, setMostrarModal] = useState(false);
  const [mostrarFiltros, setMostrarFiltros] = useState(false);

  const {
    agendamentos,
    estatisticas,
    loading,
    error,
    atualizando,
    filtros,
    buscarAgendamentos,
    confirmarAgendamento,
    recusarAgendamento,
    cancelarAgendamento,
    concluirAgendamento,
    marcarComoPago,
    aplicarFiltros,
    limparFiltros,
    limparErro
  } = useGerenciamentoAgendamentos();

  // Carregar agendamentos do colaborador ao montar o componente
  useEffect(() => {
    if (user?.id) {
      // Filtrar apenas agendamentos onde o usuário é o colaborador
      buscarAgendamentos({ colaborador_user_id: user.id });
    }
  }, [buscarAgendamentos, user?.id]);

  const handleVerDetalhes = (agendamento: AgendamentoCompleto) => {
    setAgendamentoSelecionado(agendamento);
    setMostrarModal(true);
  };

  const handleFecharModal = () => {
    setMostrarModal(false);
    setAgendamentoSelecionado(null);
  };

  const handleConfirmar = async (id: number) => {
    const sucesso = await confirmarAgendamento(id);
    if (sucesso) {
      // Recarregar dados para atualizar estatísticas
      buscarAgendamentos({ colaborador_user_id: user?.id });
    }
  };

  const handleRecusar = async (id: number) => {
    const sucesso = await recusarAgendamento(id);
    if (sucesso) {
      buscarAgendamentos({ colaborador_user_id: user?.id });
    }
  };

  const handleCancelar = async (id: number) => {
    const sucesso = await cancelarAgendamento(id);
    if (sucesso) {
      buscarAgendamentos({ colaborador_user_id: user?.id });
    }
  };

  const handleConcluir = async (id: number) => {
    const sucesso = await concluirAgendamento(id);
    if (sucesso) {
      buscarAgendamentos({ colaborador_user_id: user?.id });
    }
  };

  const handleMarcarPago = async (id: number) => {
    const sucesso = await marcarComoPago(id);
    if (sucesso) {
      buscarAgendamentos({ colaborador_user_id: user?.id });
    }
  };

  const handleAplicarFiltros = (novosFiltros: any) => {
    // Sempre manter o filtro do colaborador
    aplicarFiltros({ ...novosFiltros, colaborador_user_id: user?.id });
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Minha Agenda
              </h1>
              <p className="text-[var(--text-secondary)]">
                Visualize e gerencie seus agendamentos
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setMostrarFiltros(!mostrarFiltros)}
              >
                {mostrarFiltros ? 'Ocultar Filtros' : 'Filtros'}
              </Button>
              <Link href="/colaborador/agenda">
                <Button variant="outline">
                  Voltar ao Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Estatísticas Rápidas */}
        {estatisticas && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-[var(--text-primary)]">
                    {estatisticas.total}
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Total
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {estatisticas.pendentes}
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Pendentes
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {estatisticas.confirmados}
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Confirmados
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {estatisticas.concluidos}
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Concluídos
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Alertas de agendamentos pendentes próximos ao prazo */}
        {agendamentos.some(a => {
          const prazo = new Date(a.prazo_confirmacao);
          const agora = new Date();
          const duasHoras = 2 * 60 * 60 * 1000;
          return a.status_agendamento === 'Pendente' && (prazo.getTime() - agora.getTime()) < duasHoras;
        }) && (
          <Card className="mb-6 border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="text-orange-600">⚠️</div>
                <div>
                  <div className="font-semibold text-orange-800">
                    Atenção: Agendamentos próximos ao prazo
                  </div>
                  <div className="text-sm text-orange-700">
                    Você tem agendamentos pendentes que precisam ser confirmados ou recusados em breve.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Próximos agendamentos do dia */}
        {agendamentos.filter(a => {
          const hoje = new Date();
          const dataAgendamento = new Date(a.data_hora_inicio);
          return dataAgendamento.toDateString() === hoje.toDateString() && 
                 ['Confirmado', 'Pendente'].includes(a.status_agendamento);
        }).length > 0 && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-800">📅 Agendamentos de Hoje</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {agendamentos
                  .filter(a => {
                    const hoje = new Date();
                    const dataAgendamento = new Date(a.data_hora_inicio);
                    return dataAgendamento.toDateString() === hoje.toDateString() && 
                           ['Confirmado', 'Pendente'].includes(a.status_agendamento);
                  })
                  .slice(0, 3)
                  .map(agendamento => (
                    <div key={agendamento.agendamento_id} className="flex items-center justify-between text-sm">
                      <div>
                        <span className="font-medium">{agendamento.servico.nome_servico}</span>
                        <span className="text-blue-600 ml-2">
                          {new Date(agendamento.data_hora_inicio).toLocaleTimeString('pt-BR', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        agendamento.status_agendamento === 'Confirmado' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {agendamento.status_agendamento}
                      </span>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Erro */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="text-red-600">❌</div>
                  <div>
                    <div className="font-semibold text-red-800">Erro</div>
                    <div className="text-sm text-red-700">{error}</div>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={limparErro}
                  className="text-red-600 border-red-300 hover:bg-red-100"
                >
                  Fechar
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filtros (sidebar) */}
          {mostrarFiltros && (
            <div className="lg:col-span-1">
              <FiltrosAgendamentos
                filtros={filtros}
                onAplicarFiltros={handleAplicarFiltros}
                onLimparFiltros={() => {
                  limparFiltros();
                  buscarAgendamentos({ colaborador_user_id: user?.id });
                }}
                loading={loading}
                mostrarFiltroColaborador={false} // Colaborador não precisa filtrar por si mesmo
              />
            </div>
          )}

          {/* Lista de agendamentos */}
          <div className={mostrarFiltros ? 'lg:col-span-3' : 'lg:col-span-4'}>
            <ListaAgendamentos
              agendamentos={agendamentos}
              onConfirmar={handleConfirmar}
              onRecusar={handleRecusar}
              onCancelar={handleCancelar}
              onConcluir={handleConcluir}
              onMarcarPago={handleMarcarPago}
              onVerDetalhes={handleVerDetalhes}
              loading={loading || atualizando}
              userRole="Colaborador"
              titulo="Meus Agendamentos"
              mostrarFiltros={false} // Filtros estão na sidebar
            />
          </div>
        </div>
      </div>

      {/* Modal de detalhes */}
      <ModalDetalhesAgendamento
        agendamento={agendamentoSelecionado}
        isOpen={mostrarModal}
        onClose={handleFecharModal}
        onConfirmar={handleConfirmar}
        onRecusar={handleRecusar}
        onCancelar={handleCancelar}
        onConcluir={handleConcluir}
        onMarcarPago={handleMarcarPago}
        loading={atualizando}
        userRole="Colaborador"
      />
    </div>
  );
}

export default function AgendamentosColaboradorPage() {
  return (
    <ProtectedRoute requiredRole="Colaborador">
      <GerenciamentoAgendamentosColaborador />
    </ProtectedRoute>
  );
}
