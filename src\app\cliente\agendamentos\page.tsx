'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useGerenciamentoAgendamentos } from '@/hooks/useGerenciamentoAgendamentos';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ListaAgendamentos } from '@/components/agendamentos/ListaAgendamentos';
import { FiltrosCliente } from '@/components/cliente/FiltrosCliente';
import { ModalDetalhesAgendamento } from '@/components/agendamentos/ModalDetalhesAgendamento';
import { AgendamentoCompleto } from '@/types/agendamentos';

export default function ClienteAgendamentosPage() {
  return (
    <ProtectedRoute requiredRole="Usuario">
      <ClienteAgendamentos />
    </ProtectedRoute>
  );
}

function ClienteAgendamentos() {
  const {
    agendamentos,
    estatisticas,
    loading,
    error,
    atualizando,
    filtros,
    buscarAgendamentos,
    aplicarFiltros,
    limparFiltros,
    limparErro
  } = useGerenciamentoAgendamentos();

  const [agendamentoDetalhes, setAgendamentoDetalhes] = useState<AgendamentoCompleto | null>(null);
  const [mostrarFiltros, setMostrarFiltros] = useState(false);

  // Carregar agendamentos ao montar o componente
  useEffect(() => {
    buscarAgendamentos();
  }, [buscarAgendamentos]);

  // Handlers
  const handleVerDetalhes = (agendamento: AgendamentoCompleto) => {
    setAgendamentoDetalhes(agendamento);
  };



  const aplicarFiltrosPersonalizados = (novosFiltros: any) => {
    aplicarFiltros(novosFiltros);
    buscarAgendamentos(novosFiltros);
  };

  const limparFiltrosPersonalizados = () => {
    limparFiltros();
    buscarAgendamentos();
  };

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Meus Agendamentos
              </h1>
              <p className="text-[var(--text-secondary)]">
                Gerencie seus agendamentos e histórico de serviços
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setMostrarFiltros(!mostrarFiltros)}
                className="hidden lg:flex"
              >
                {mostrarFiltros ? 'Ocultar Filtros' : 'Mostrar Filtros'}
              </Button>
              <Link href="/cliente/dashboard">
                <Button variant="outline">
                  Voltar ao Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Estatísticas */}
      {estatisticas && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Total</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Pendentes</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.pendentes}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Confirmados</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.confirmados}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Concluídos</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.concluidos}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Erro */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
          <div className="bg-[var(--error-light)] border border-[var(--error)] rounded-lg p-4">
            <div className="flex justify-between items-center">
              <p className="text-[var(--error)]">{error}</p>
              <Button variant="outline" size="sm" onClick={limparErro}>
                Fechar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Conteúdo Principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filtros (sidebar) */}
          {mostrarFiltros && (
            <div className="lg:col-span-1">
              <FiltrosCliente
                filtros={filtros}
                onAplicarFiltros={aplicarFiltrosPersonalizados}
                onLimparFiltros={limparFiltrosPersonalizados}
                loading={loading}
              />
            </div>
          )}

          {/* Lista de agendamentos */}
          <div className={mostrarFiltros ? 'lg:col-span-3' : 'lg:col-span-4'}>
            <ListaAgendamentos
              agendamentos={agendamentos}
              onVerDetalhes={handleVerDetalhes}
              loading={loading || atualizando}
              userRole="Usuario"
              titulo="Meus Agendamentos"
              mostrarFiltros={false} // Filtros estão na sidebar
            />
          </div>
        </div>
      </div>

      {/* Modal de detalhes */}
      {agendamentoDetalhes && (
        <ModalDetalhesAgendamento
          isOpen={!!agendamentoDetalhes}
          agendamento={agendamentoDetalhes}
          onClose={() => setAgendamentoDetalhes(null)}
          userRole="Usuario"
        />
      )}
    </div>
  );
}
